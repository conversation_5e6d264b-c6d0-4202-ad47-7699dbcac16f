# Big Event 项目技术文档

## 1. 项目概述

Big Event 是一个基于 Spring Boot 的文章管理系统，提供用户注册登录、文章分类管理、文章发布与管理等功能。该系统采用前后端分离架构，后端提供 RESTful API 接口。

## 2. 开发技术介绍

### 2.1 核心技术栈

- **后端框架**: Spring Boot 3.4.1
- **Java版本**: JDK 17
- **数据库**: MySQL 8.0
- **缓存**: Redis
- **持久层框架**: MyBatis 3.0.4
- **构建工具**: Maven
- **认证授权**: JWT (JSON Web Token)
- **数据验证**: Spring Boot Validation
- **分页插件**: PageHelper 1.4.7

### 2.2 主要依赖

```xml
<!-- Spring Boot Web 起步依赖 -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-web</artifactId>
</dependency>

<!-- MyBatis 起步依赖 -->
<dependency>
    <groupId>org.mybatis.spring.boot</groupId>
    <artifactId>mybatis-spring-boot-starter</artifactId>
    <version>3.0.4</version>
</dependency>

<!-- MySQL 驱动 -->
<dependency>
    <groupId>com.mysql</groupId>
    <artifactId>mysql-connector-j</artifactId>
</dependency>

<!-- Redis 起步依赖 -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-data-redis</artifactId>
</dependency>

<!-- JWT 依赖 -->
<dependency>
    <groupId>com.auth0</groupId>
    <artifactId>java-jwt</artifactId>
    <version>4.4.0</version>
</dependency>

<!-- 数据验证依赖 -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-validation</artifactId>
</dependency>

<!-- Lombok 依赖 -->
<dependency>
    <groupId>org.projectlombok</groupId>
    <artifactId>lombok</artifactId>
</dependency>
```

## 3. 开发环境和开发工具介绍

### 3.1 开发环境要求

- **JDK**: 17 或以上版本
- **Maven**: 3.6+ 
- **MySQL**: 8.0+
- **Redis**: 6.0+
- **IDE**: IntelliJ IDEA 或 Eclipse

### 3.2 数据库配置

```yaml
spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *************************************
    username: root
    password: 2005717
  data:
    redis:
      host: 127.0.0.1
      port: 6379
      password:
```

### 3.3 MyBatis 配置

```yaml
mybatis:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    map-underscore-to-camel-case: true
  mapper-locations: classpath:/mapper/*.xml
  type-aliases-package: text.pojo
```

## 4. 系统用例建模

### 4.1 需求获取

#### 4.1.1 功能性需求

1. **用户管理**
   - 用户注册：新用户可以注册账号
   - 用户登录：已注册用户可以登录系统
   - 用户信息管理：用户可以修改个人信息、头像、密码

2. **文章分类管理**
   - 分类创建：用户可以创建文章分类
   - 分类查询：用户可以查看自己创建的分类列表
   - 分类修改：用户可以修改分类信息
   - 分类删除：用户可以删除不需要的分类

3. **文章管理**
   - 文章发布：用户可以发布新文章
   - 文章查询：用户可以查看文章列表，支持分页和条件查询
   - 文章修改：用户可以修改自己发布的文章
   - 文章删除：用户可以删除自己发布的文章

4. **文件上传**
   - 图片上传：支持文章封面图片上传

#### 4.1.2 非功能性需求

1. **安全性**
   - JWT Token 认证
   - 密码 MD5 加密
   - 登录拦截器验证

2. **性能**
   - Redis 缓存 Token
   - 分页查询优化
   - 数据库连接池

3. **可维护性**
   - 分层架构设计
   - 统一异常处理
   - 统一响应格式

### 4.2 用例图

```
                    Big Event 系统用例图
    
    用户 ──────────── 注册
     │
     ├─────────── 登录
     │
     ├─────────── 修改个人信息
     │
     ├─────────── 修改头像
     │
     ├─────────── 修改密码
     │
     ├─────────── 创建文章分类
     │
     ├─────────── 查看分类列表
     │
     ├─────────── 修改分类
     │
     ├─────────── 删除分类
     │
     ├─────────── 发布文章
     │
     ├─────────── 查看文章列表
     │
     ├─────────── 修改文章
     │
     ├─────────── 删除文章
     │
     └─────────── 上传文件
```

### 4.3 主要用例描述

#### 4.3.1 用户注册用例

- **用例名称**: 用户注册
- **参与者**: 未注册用户
- **前置条件**: 用户未在系统中注册
- **主要流程**:
  1. 用户输入用户名和密码
  2. 系统验证用户名格式（2-16位非空字符）
  3. 系统检查用户名是否已存在
  4. 系统对密码进行MD5加密
  5. 系统保存用户信息
  6. 返回注册成功信息
- **异常流程**: 用户名已存在时返回错误信息

#### 4.3.2 用户登录用例

- **用例名称**: 用户登录
- **参与者**: 已注册用户
- **前置条件**: 用户已在系统中注册
- **主要流程**:
  1. 用户输入用户名和密码
  2. 系统验证用户名格式
  3. 系统查询用户信息
  4. 系统验证密码（MD5加密后比较）
  5. 系统生成JWT Token
  6. 系统将Token存储到Redis
  7. 返回Token给用户
- **异常流程**: 用户名不存在或密码错误时返回错误信息

#### 4.3.3 文章发布用例

- **用例名称**: 文章发布
- **参与者**: 已登录用户
- **前置条件**: 用户已登录系统
- **主要流程**:
  1. 用户输入文章标题、内容、分类等信息
  2. 系统验证文章信息格式
  3. 系统从Token中获取用户信息
  4. 系统设置文章创建时间和创建用户
  5. 系统保存文章信息
  6. 返回发布成功信息
- **异常流程**: 文章信息验证失败时返回错误信息

## 5. 系统业务流程图

### 5.1 用户认证流程

```
开始 → 用户输入用户名密码 → 验证用户名格式 → 查询用户信息 
  ↓
用户不存在? → 是 → 返回用户名错误
  ↓ 否
验证密码 → 密码错误? → 是 → 返回密码错误
  ↓ 否
生成JWT Token → 存储Token到Redis → 返回Token → 结束
```

### 5.2 文章管理流程

```
开始 → 验证Token → Token无效? → 是 → 返回未登录错误
  ↓ 否
解析用户信息 → 执行业务操作（增删改查）→ 返回操作结果 → 结束
```

### 5.3 请求拦截流程

```
请求到达 → 登录拦截器 → 是登录/注册请求? → 是 → 放行
  ↓ 否
获取Authorization Header → Token存在? → 否 → 返回401
  ↓ 是
从Redis验证Token → Token有效? → 否 → 返回401
  ↓ 是
解析Token获取用户信息 → 存储到ThreadLocal → 放行 → 业务处理
```

## 6. 类图设计

### 6.1 系统整体架构

系统采用经典的三层架构模式：
- **Controller层**: 处理HTTP请求，参数验证，调用Service层
- **Service层**: 业务逻辑处理，事务管理
- **Mapper层**: 数据访问层，与数据库交互

### 6.2 核心实体类

#### 6.2.1 User（用户实体）

```java
@Data
public class User {
    @NotNull
    private Integer id;           // 主键ID
    private String username;      // 用户名
    @JsonIgnore
    private String password;      // 密码（MD5加密）
    @NotEmpty
    @Pattern(regexp="^\\S{1,10}$")
    private String nickname;      // 昵称
    @NotEmpty
    @Email
    private String email;         // 邮箱
    private String userPic;       // 用户头像地址
    private LocalDateTime createTime;  // 创建时间
    private LocalDateTime updateTime;  // 更新时间
}
```

#### 6.2.2 Category（分类实体）

```java
@Data
public class Category {
    @NotNull(groups = Update.class)
    private Integer id;           // 主键ID
    @NotEmpty
    private String categoryName;  // 分类名称
    @NotEmpty
    private String categoryAlias; // 分类别名
    private Integer createUser;   // 创建人ID
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;  // 创建时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;  // 更新时间

    // 验证分组接口
    public interface Add extends Default {}
    public interface Update extends Default {}
}
```

#### 6.2.3 Article（文章实体）

```java
@Data
public class Article {
    private Integer id;           // 主键ID
    @NotEmpty
    @Pattern(regexp = "^\\S{1,20}$")
    private String title;         // 文章标题
    @NotEmpty
    private String content;       // 文章内容
    private String coverImg;      // 封面图像
    @State
    private String state;         // 发布状态（已发布|草稿）
    @NotNull
    private Integer categoryId;   // 文章分类ID
    private Integer createUser;   // 创建人ID
    private LocalDateTime createTime;  // 创建时间
    private LocalDateTime updateTime;  // 更新时间
}
```

#### 6.2.4 Result（统一响应结果）

```java
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Result<T> {
    private Integer code;         // 业务状态码 0-成功 1-失败
    private String message;       // 提示信息
    private T data;              // 响应数据

    // 静态方法
    public static <E> Result<E> success(E data);
    public static Result success();
    public static Result error(String message);
}
```

### 6.3 控制器类设计

#### 6.3.1 UserController（用户控制器）

```java
@Slf4j
@RestController
@RequestMapping("/user")
@Validated
public class UserController {
    @Autowired
    private UserService userService;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    // 用户注册
    @PostMapping("/register")
    public Result register(@Pattern(regexp = "^\\S{2,16}$") String username, String password);

    // 用户登录
    @PostMapping("/login")
    public Result login(@Pattern(regexp = "^\\S{2,16}$") String username, String password);

    // 获取用户信息
    @GetMapping("/userInfo")
    public Result<User> userInfo();

    // 更新用户信息
    @PutMapping("/update")
    public Result update(@RequestBody @Validated User user);

    // 更新用户头像
    @PatchMapping("/updateAvatar")
    public Result updateAvatar(@RequestParam @URL String avatarUrl);

    // 更新用户密码
    @PatchMapping("/updatePwd")
    public Result updatePwd(@RequestBody Map<String,String> params);
}
```

#### 6.3.2 CategoryController（分类控制器）

```java
@RestController
@RequestMapping("/category")
public class CategoryController {
    @Autowired
    private CategoryService categoryService;

    // 新增分类
    @PostMapping
    public Result add(@RequestBody @Validated(Category.Add.class) Category category);

    // 分类列表
    @GetMapping
    public Result<List<Category>> list();

    // 分类详情
    @GetMapping("/detail")
    public Result<Category> detail(@RequestParam Integer id);

    // 更新分类
    @PutMapping
    public Result update(@RequestBody @Validated(Category.Update.class) Category category);

    // 删除分类
    @DeleteMapping
    public Result delete(@RequestParam Integer id);
}
```

#### 6.3.3 ArticleController（文章控制器）

```java
@Slf4j
@RestController
@RequestMapping("/article")
public class ArticleController {
    @Autowired
    private ArticleService articleService;

    // 新增文章
    @PostMapping
    public Result add(@RequestBody Article article);

    // 文章列表（分页查询）
    @GetMapping
    public Result<PageBean<Article>> list(Integer pageNum, Integer pageSize,
                                         @RequestParam(required = false) Integer categoryId,
                                         @RequestParam(required = false) String state);

    // 文章详情
    @GetMapping("/{id}")
    public Result<Article> get(@PathVariable Integer id);

    // 更新文章
    @PutMapping
    public Result update(@RequestBody Article article);

    // 删除文章
    @DeleteMapping("/{id}")
    public Result delete(@PathVariable Integer id);
}
```

### 6.4 服务层接口设计

#### 6.4.1 UserService（用户服务接口）

```java
public interface UserService {
    // 根据用户名查找用户
    User findByUserName(String username);

    // 用户注册
    void register(String username, String password);

    // 更新用户信息
    void update(User user);

    // 更新用户头像
    void updateAvatar(String avatarUrl);

    // 更新用户密码
    void updatePwd(String newPwd);
}
```

#### 6.4.2 CategoryService（分类服务接口）

```java
public interface CategoryService {
    // 添加分类
    void add(Category category);

    // 列表查询
    List<Category> list();

    // 根据ID查询分类信息
    Category findById(Integer id);

    // 更新分类
    void update(Category category);

    // 删除分类
    void delete(Integer id);
}
```

#### 6.4.3 ArticleService（文章服务接口）

```java
public interface ArticleService {
    // 新增文章
    void add(Article article);

    // 条件分页列表查询
    PageBean<Article> list(Integer pageNum, Integer pageSize,
                          Integer categoryId, String state);

    // 根据ID查询文章
    Article findById(Integer id);

    // 修改文章
    void update(Article article);

    // 删除文章
    void delete(Integer id);
}
```

### 6.5 数据访问层设计

#### 6.5.1 UserMapper（用户数据访问接口）

```java
@Mapper
public interface UserMapper {
    // 根据用户名查询用户
    @Select("select * from user where username=#{username}")
    User findByUserName(String username);

    // 新增用户
    @Insert("insert into user(username,password,create_time,update_time) " +
            "values(#{username},#{password},now(),now())")
    void add(String username, String password);

    // 更新用户信息
    @Update("update user set nickname=#{nickname},email=#{email}," +
            "update_time=#{updateTime} where id=#{id}")
    void update(User user);

    // 更新用户头像
    @Update("update user set user_pic=#{avatarUrl},update_time=now() where id=#{id}")
    void updateAvatar(String avatarUrl, Integer id);

    // 更新用户密码
    @Update("update user set password=#{newPwd},update_time=now() where id=#{id}")
    void updatePwd(String newPwd, Integer id);
}
```

### 6.6 工具类设计

#### 6.6.1 JwtUtil（JWT工具类）

```java
public class JwtUtil {
    private static final String KEY = "itheima";

    // 生成Token
    public static String genToken(Map<String, Object> claims);

    // 解析Token
    public static Map<String, Object> parseToken(String token);
}
```

#### 6.6.2 Md5Util（MD5加密工具类）

```java
public class Md5Util {
    // 生成字符串的MD5校验值
    public static String getMD5String(String s);

    // 验证密码
    public static boolean checkPassword(String password, String md5PwdStr);
}
```

#### 6.6.3 ThreadLocalUtil（线程本地存储工具类）

```java
public class ThreadLocalUtil {
    private static final ThreadLocal THREAD_LOCAL = new ThreadLocal();

    // 获取值
    public static <T> T get();

    // 存储值
    public static void set(Object value);

    // 清除值
    public static void remove();
}
```

### 6.7 拦截器和配置类

#### 6.7.1 LoginInterceptor（登录拦截器）

```java
@Component
public class loginInterceptors implements HandlerInterceptor {
    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Override
    public boolean preHandle(HttpServletRequest request,
                           HttpServletResponse response,
                           Object handler) throws Exception;

    @Override
    public void afterCompletion(HttpServletRequest request,
                              HttpServletResponse response,
                              Object handler, Exception ex) throws Exception;
}
```

#### 6.7.2 WebConfig（Web配置类）

```java
@Configuration
public class WebConfig implements WebMvcConfigurer {
    @Autowired
    private loginInterceptors loginInterceptors;

    @Override
    public void addInterceptors(InterceptorRegistry registry);
}
```

### 6.8 自定义注解和验证器

#### 6.8.1 @State注解

```java
@Documented
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = {StateValidation.class})
public @interface State {
    String message() default "state参数必须是已发布或草稿";
    Class<?>[] groups() default {};
    Class<? extends Payload>[] payload() default {};
}
```

#### 6.8.2 StateValidation验证器

```java
public class StateValidation implements ConstraintValidator<State,String> {
    @Override
    public boolean isValid(String s, ConstraintValidatorContext context);
}
```

## 7. 系统特性

### 7.1 安全特性

1. **JWT认证**: 使用JWT Token进行用户身份验证
2. **密码加密**: 使用MD5对用户密码进行加密存储
3. **Token缓存**: 将Token存储在Redis中，支持Token失效管理
4. **请求拦截**: 通过拦截器验证用户登录状态

### 7.2 性能特性

1. **分页查询**: 使用PageHelper实现高效的分页查询
2. **缓存机制**: 使用Redis缓存Token，减少数据库查询
3. **连接池**: 使用数据库连接池提高数据库访问效率

### 7.3 可维护性特性

1. **分层架构**: 清晰的Controller-Service-Mapper三层架构
2. **统一异常处理**: 全局异常处理器统一处理系统异常
3. **统一响应格式**: 使用Result类统一API响应格式
4. **参数验证**: 使用Bean Validation进行参数验证

## 8. 部署说明

### 8.1 环境准备

1. 安装JDK 17
2. 安装MySQL 8.0并创建big_event数据库
3. 安装Redis
4. 配置application.yml中的数据库和Redis连接信息

### 8.2 构建和运行

```bash
# 构建项目
mvn clean package

# 运行项目
java -jar target/big-event-0.0.1-SNAPSHOT.jar
```

### 8.3 API访问

- 基础URL: http://localhost:8080
- 用户注册: POST /user/register
- 用户登录: POST /user/login
- 文章管理: /article/*
- 分类管理: /category/*

## 9. 总结

Big Event项目是一个功能完整的文章管理系统，采用了现代化的Java技术栈，具有良好的架构设计和代码组织。系统实现了用户管理、文章管理、分类管理等核心功能，并提供了完善的安全认证机制。通过合理的分层设计和工具类封装，系统具有良好的可维护性和扩展性。
