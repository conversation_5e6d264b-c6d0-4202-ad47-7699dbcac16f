package text;

import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import org.junit.jupiter.api.Test;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

public class JwtTest {
    @Test
    public  void textGen() {
        Map<String, Object> map = new HashMap<>();
        map.put("id", "1");
        map.put("username", "凤九歌");
        String jwt= JWT.create()
                .withClaim("user",map)//添加载荷
                .withExpiresAt(new Date(System.currentTimeMillis()+1000*60*60*12))//生存周期
                .sign(Algorithm.HMAC256("wmq"));//制定算法，配置秘钥
    }
}
