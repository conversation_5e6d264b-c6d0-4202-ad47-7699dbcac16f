package text;

import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;

@SpringBootTest() // 确保加载 Redis 配置类
public class RedisTest {

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Test
    public void testSet() {
        // 存储键对值
        ValueOperations<String, String> operations = stringRedisTemplate.opsForValue();
        operations.set("username", "zhang<PERSON>");
    }
}
