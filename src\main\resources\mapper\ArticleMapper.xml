<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="text.mapper.ArticleMapper">
    <resultMap id="ArticleMapper" type="Article">
        <id column="id" property="id"/>
        <result column="title" property="title"/>
        <result column="content" property="content"/>
        <result column="cover_img" property="coverImg"/>
        <result column="state" property="state"/>
        <result column="category_id" property="categoryId"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>
    <insert id="add">
        insert into article(title, content, cover_img, state, category_id, create_user, create_time, update_time)
            values(#{title},#{content},#{coverImg},#{state},#{categoryId},#{createUser},#{createTime},#{updateTime})
    </insert>
    <update id="update">
        update article set title=#{title},content=#{content},cover_img=#{coverImg} where id=#{id}
    </update>
    <delete id="delete">
        delete from  article where id=#{id}
    </delete>
    <!--动态SQL-->
    <select id="list" resultType="Article" resultMap="ArticleMapper">
        select * from article
        <where>
            <if test="categoryId!=null">
                category_id=#{categoryId}
            </if>
            <if test="state!=null">
                and state=#{state}
            </if>
            and create_user=#{userid}
        </where>
    </select>
    <select id="findById" resultMap="ArticleMapper">
        SELECT *
        FROM article
        WHERE id=#{id}
    </select>
</mapper>