package text.validation;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import text.anno.State;


public class StateValidation implements ConstraintValidator<State,String> {
    /**
     *
     * @param s 将来要校验的值
     * @param constraintValidatorContext s
     * @return 如果返回false，则校检不通过，ture则通过
     */
    @Override
    public boolean isValid(String s, ConstraintValidatorContext constraintValidatorContext) {
        if (s==null){
            return false;}
        if (s.equals("已发布")||s.equals("草稿")){
            return true;}
        return false;
    }
}
