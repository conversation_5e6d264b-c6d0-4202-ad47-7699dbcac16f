package text.service.impl;

import text.pojo.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import text.mapper.UserMapper;
import text.service.UserService;
import text.utils.Md5Util;
import text.utils.ThreadLocalUtil;

import java.time.LocalDateTime;
import java.util.Map;


@Service
public class UserServiceImpl implements UserService {
    @Autowired
    private UserMapper userMapper;
    @Override
    public User findByUserName(String username) {
        User u= userMapper.findByUserName(username);
        return u;
    }

    @Override
    public void register(String username, String password) {
        //加密
        String md5str= Md5Util.getMD5String(password);
        userMapper.add(username,md5str);
    }

    @Override
    public void update(User user) {
        user.setUpdateTime(LocalDateTime.now());
        userMapper.update(user);
    }

    @Override
    public void updateAvatar(String avatarUrl) {
        Map<String,Object> map= ThreadLocalUtil.get();
        Integer id=(Integer) map.get("id");
        userMapper.updateAvatar(avatarUrl,id);
    }

    @Override
    public void updatePwd(String newPwd) {
        Map<String,Object> ma= ThreadLocalUtil.get();
        Integer id=(Integer) ma.get("id");
        userMapper.updatePwd(Md5Util.getMD5String(newPwd),id);
    }
}
