package text.mapper;

import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import text.pojo.Article;

import java.util.List;

@Mapper
public interface ArticleMapper {
    //新增
    void add(Article article);

    //列表查询
    List<Article> list(Integer userid, Integer categoryId, String state);

    //修改文章
    void update(Article article);

    //删除
    void delete(Integer id);
    Article findById(Integer id);
}
