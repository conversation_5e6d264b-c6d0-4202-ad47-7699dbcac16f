spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *************************************
    username: root
    password: 2005717
  data:
    redis:
      host: 127.0.0.1
      port: 6379
      password:
  devtools:
    restart:
      enabled: true
      additional-paths: src/main/java

mybatis:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl #显示日志
    map-underscore-to-camel-case: true #开启驼峰命名和下划线命名的自动转换
  mapper-locations: classpath:/mapper/*.xml
  type-aliases-package: text.pojo