package text.controller;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import text.pojo.Article;
import text.pojo.PageBean;
import text.pojo.Result;
import text.service.ArticleService;

@Slf4j
@RestController
@RequestMapping("/article")
public class ArticleController {
/*    @GetMapping("/list")
    public Result<String> list(*//*@RequestHeader(name = "")String token, HttpServletResponse response*//*) {
        /验证token
        try {
            Map<String, Object> map = JwtUtil.parseToken(token);
            return Result.success();
        } catch (Exception e) {
            response.setStatus(401);
            return Result.error("未登录");
        }
        return Result.success(".....");
    }*/

    @Autowired
    private ArticleService articleService;

    @PostMapping
    public Result add(@RequestBody Article article) {
        log.info("接受参数>>{}",article);
        articleService.add(article);
        return Result.success();
    }

    @GetMapping
    public Result<PageBean<Article>> list(
            Integer pageNum, Integer pageSize,
            @RequestParam(required = false) Integer categoryId,
            @RequestParam(required = false) String state
    ) {
        PageBean<Article> pg= articleService.list(pageNum,pageSize,categoryId,state);
        return Result.success(pg);
    }

    @GetMapping("/{id}")
    public Result<Article> get(@PathVariable Integer id) {
        return Result.success(articleService.findById(id));
    }

    @PutMapping
    public Result update(@RequestBody Article article) {
        articleService.update(article);
        return Result.success();
    }

    @DeleteMapping
    public Result delete(@RequestParam Integer id) {
        articleService.delete(id);
        return Result.success();
    }
}
