package text.controller;

import jakarta.validation.constraints.Pattern;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.validator.constraints.URL;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import text.pojo.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import text.pojo.Result;
import text.service.UserService;
import text.utils.JwtUtil;
import text.utils.Md5Util;
import text.utils.ThreadLocalUtil;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Slf4j
@RestController
@RequestMapping("/user")
@Validated
public class UserController {

    @Autowired
    private UserService userService;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @PostMapping("/register")
    public Result register(@Pattern(regexp = "^\\S{2,16}$") String username, String password) {
        //查询用户
       User u= userService.findByUserName(username);
       if (u==null) {
           userService.register(username,password);
           return Result.success();
       }else {
           return Result.error("用户名已存在");
       }
    }

    @PostMapping("/login")
    public Result login(@Pattern(regexp = "^\\S{2,16}$") String username, String password){
        User u= userService.findByUserName(username);
        if (u==null) {
            return Result.error("用户名错误");
        }
        //判断密码是否正确,u中的password是明文
        if (Md5Util.getMD5String(password).equals(u.getPassword())) {
            //登录成功
            Map<String,Object> map = new HashMap<>();
            map.put("id",u.getId());
            map.put("username",u.getUsername());
            String token = JwtUtil.genToken(map);
            //将token存储到redis
            ValueOperations<String, String> stringStringValueOperations = stringRedisTemplate.opsForValue();
            stringStringValueOperations.set(token,token,1, TimeUnit.DAYS);

            return Result.success(token);
        }
        return Result.success("密码错误");
    }

    @GetMapping("/userInfo")
    public Result<User> userInfo(/*@RequestHeader(name = "Authorization") String token*/) {
        //根据用户名查找用户
        /*Map<String,Object> map = JwtUtil.parseToken(token);
        String username = (String) map.get("username");*/

        Map<String,Object> map= ThreadLocalUtil.get();
        String username=(String) map.get("username");
        User user = userService.findByUserName(username);
        return Result.success(user);
    }

    @PutMapping("/update")
    public Result update(@RequestBody @Validated User user) {
        userService.update(user);
        return Result.success();
    }

    @PatchMapping("/updateAvatar")
    public Result updateAvatar(@RequestParam @URL String avatarUrl) {
        userService.updateAvatar(avatarUrl);
        return Result.success();
    }

    @PatchMapping("/updatePwd")
    public Result updatePwd(@RequestBody Map<String,String> map,@RequestHeader("Authorization") String token) {
        //校检参数
        String oldPwd=map.get("old_pwd");
        String newPwd=map.get("new_pwd");
        String rePwd=map.get("re_pwd");

        if (!StringUtils.hasLength(oldPwd) || !StringUtils.hasLength(newPwd) || !StringUtils.hasLength(rePwd)) {
            return Result.error("缺少必要参数");
        }

        //原密码是否真确
        //调用user service根据用户名拿到密码，和原密码比对
        Map<String,Object> m=ThreadLocalUtil.get();
        String username=(String) m.get("username");
        User user=userService.findByUserName(username);
        if (!user.getPassword().equals(Md5Util.getMD5String(oldPwd))) {
            return Result.error("原密码不一致");
        }

        //判读新密码是否一致
        if (!rePwd.equals(newPwd)) {
            return  Result.error(("输入密码不一致"));
        }
        //调用service完成密码更新
        userService.updatePwd(newPwd);
        //删除redis中对应的token
        stringRedisTemplate.delete(token);
        return Result.success();
    }
}
