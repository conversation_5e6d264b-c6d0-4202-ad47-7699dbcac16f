package text.anno;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import text.validation.StateValidation;

import java.lang.annotation.*;

@Documented //元注解
@Target({ElementType.FIELD}) //元注解
@Retention(RetentionPolicy.RUNTIME) //元注解
@Constraint(validatedBy = {StateValidation.class})//指定提供校检规则的类
public @interface State {
    //提供校检失败后的提示信息
    String message() default "state参数必须是已发布或草稿";
    //指定分组
    Class<?>[] groups() default {};
    //负载 获取到State注解的附加信息
    Class<? extends Payload>[] payload() default {};
}
